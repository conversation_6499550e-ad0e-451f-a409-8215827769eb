<?php
require_once("../../config/Session.php");
require_once("../../config/mainModel.php");

class asignar_dcs
{
	private $DB, $user;
	public function __construct()
	{
		$this->DB = new BD();
		$this->DB->conectar();
		$oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
		$this->user = $oSession->VSid;
	}
	public function __destruct()
	{
		$this->DB->desconectar();
	}

	//Espacio Para declara las funciones que retornan los datos de la DB.

	public function cargar_campos()
	{
		$sql = "SELECT tipo, campo, nombre FROM {$GLOBALS["BD_NAME"]}.configuracion__campos ORDER BY id";
		$resp = $this->DB->devolver_array($sql);

		return $resp;
	}

	public function Guardar_Auditoria($tipo, $id_tipo, $p1, $p2, $p3, $p4, $p5, $movi, $cedula, $id)
	{
		$id_usu = $this->retornar_id($cedula, $id);

		$sql = "INSERT INTO audi_niveles_dcs (tipo, id_tipo, id_usus, p1, p2, p3, p4, p5, fecha, hora, usuario, movimiento) 
						VALUES ('$tipo', '$id_tipo', '$id_usu', '" . intval($p1) . "', '" . intval($p2) . "', '" . intval($p3) . "', '" . intval($p4) . "', '" . intval($p5) . "', curdate(), curtime(),'$this->user','$movi')";
		//echo $sql;
		$this->DB->consultar($sql);
	}

	public function audi_paralela($tipo, $id, $cedula, $usuario, $movimiento)
	{

		if ($tipo == 2) {
			$id = explode("_", $id);
			$id_regional = $id[0];
			$id_canal = $id[1];
			$sql = "INSERT INTO audi_niveles_dcs_detallado (tipo, id_tipo, id_usus,fecha, hora, usuario, movimiento,p5) 
			VALUES ('$tipo', '$id_canal', '$usuario',curdate(), curtime(),'$this->user',$movimiento,$id_regional)";
			$this->DB->consultar($sql);
		} else if ($tipo == 3) {
			$id = explode("_", $id);
			$id_regional = $id[0];
			$id_canal = $id[1];
			$id_distri = $id[2];
			$sql = "INSERT INTO audi_niveles_dcs_detallado (tipo, id_tipo, id_usus,fecha, hora, usuario, movimiento,p5,p4) 
			VALUES ('$tipo', '$id_distri', '$usuario',curdate(), curtime(),'$this->user',$movimiento,$id_regional,$id_canal)";
			$this->DB->consultar($sql);
		} else if ($tipo == 1) {
			$sql = "INSERT INTO audi_niveles_dcs_detallado (tipo, id_tipo, id_usus,fecha, hora, usuario, movimiento) 
			VALUES ('$tipo', '$id', '$usuario',curdate(), curtime(),'$this->user',$movimiento)";
			$this->DB->consultar($sql);
		}
	}

	public function audi_paralela_territorio($regional, $canal, $distri, $id_terr, $usuario, $movimiento)
	{

		$sql = "INSERT INTO audi_niveles_dcs_detallado (tipo, id_tipo, id_usus,fecha, hora, usuario, movimiento,p5,p4,p3) 
			VALUES ('4', '$id_terr', '$usuario',curdate(), curtime(),'$this->user',$movimiento,$regional,$canal,$distri)";
		$this->DB->consultar($sql);
	}
	public function audi_paralela_zonas($regional, $canal, $distri, $id_terr, $id_zona, $usuario, $movimiento)
	{

		$sql = "INSERT INTO audi_niveles_dcs_detallado (tipo, id_tipo, id_usus,fecha, hora, usuario, movimiento,p5,p4,p3,p2) 
		VALUES ('5', '$id_zona', '$usuario',curdate(), curtime(),'$this->user',$movimiento,$regional,$canal,$distri,$id_terr)";
		$this->DB->consultar($sql);
	}

	public function retornar_id($cedula, $id)
	{
		$res = 0;
		$condicion = "";
		if ($id == "" && $cedula == "") {
			$response = array("draw" => 1, "recordsTotal" => 0, "recordsFiltered" => 0, "data" => "");
			die(json_encode($response));
		} else {
			if ($id != "" && $cedula != "") {
				$condicion = "AND id='$id' AND cedula = '$cedula'";
			} else if ($id != "" && $cedula == "") {
				$condicion = "AND id= '$id'";
			} else if ($id == "" && $cedula != "") {
				$condicion = "AND cedula = '$cedula'";
			}
		}
		if ($condicion != "") {
			$sql = "SELECT id FROM usuarios WHERE estado = 1 AND id != 1 $condicion";
			$datos = $this->DB->devolver_array($sql);
			if (count($datos) > 0) {
				$res = $datos[0]['id'];
			} else {
				$response = array("draw" => 1, "recordsTotal" => 0, "recordsFiltered" => 0, "data" => "");
				die(json_encode($response));
			}
		}
		return $res;
	}

	public function mostrar_regionales($cedula, $id)
	{
		$id_usu = $this->retornar_id($cedula, $id);
		//die($id_usu);
		$consulta = "SELECT id,nombre,estado FROM regional";
		$datos_regional = $this->DB->devolver_array($consulta);
		for ($r = 0; $r < count($datos_regional); $r++) {
			$id_regional = $datos_regional[$r]['id'];
			$valida_r = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 1 AND id_tipo = '$id_regional'");
			if (count($valida_r) > 0) {
				$datos_regional[$r]['permiso'] = 1;
			} else {
				$valida_r = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p5 = '$id_regional'");
				if (count($valida_r) > 0) {
					$datos_regional[$r]['permiso'] = 2;
				} else {
					$datos_regional[$r]['permiso'] = 3;
				}
			}
		}
		return $datos_regional;
	}

	public function mostrar_canales($regional, $cedula, $id)
	{
		$id_usu = $this->retornar_id($cedula, $id);
		$consulta = "SELECT c.id,c.nombre,c.estado FROM canal c, canal_regional rc WHERE c.id = 1 AND rc.id_canal = c.id AND id_regional = '$regional'";
		$datos_canales = $this->DB->devolver_array($consulta);
		for ($c = 0; $c < count($datos_canales); $c++) {
			$id_canal = $datos_canales[$c]['id'];

			$valida_regional = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 1 AND id_tipo = '$regional'");
			if (count($valida_regional) > 0) {
				$datos_canales[$c]['permiso'] = 1;
			} else {
				$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 2 AND id_tipo = '$id_canal' AND p5 = '$regional'");
				if (count($valida_c) > 0) {
					$datos_canales[$c]['permiso'] = 1;
				} else {
					$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p4 = '$id_canal' AND p5 = '$regional'");
					if (count($valida_c) > 0) {
						$datos_canales[$c]['permiso'] = 2;
					} else {
						$datos_canales[$c]['permiso'] = 3;
					}
				}
			}
		}
		return $datos_canales;
	}

	public function mostrar_distribuidores($canal, $regional, $cedula, $id)
	{
		$id_usu = $this->retornar_id($cedula, $id);
		$consulta = "SELECT d.id,d.nombre,d.estado FROM distribuidores d, regionales_distri rd WHERE rd.id_distri = d.id AND rd.id_reg = '$regional'";
		$datos_distri = $this->DB->devolver_array($consulta);

		for ($c = 0; $c < count($datos_distri); $c++) {
			$id_distri = $datos_distri[$c]['id'];
			$valida_regional = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 1 AND id_tipo = '$regional'");
			if (count($valida_regional) > 0) {
				$datos_distri[$c]['permiso'] = 1;
			} else {
				$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 2 AND id_tipo = '$canal' AND p5 = '$regional'");
				if (count($valida_c) > 0) {
					$datos_distri[$c]['permiso'] = 1;
				} else {
					$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p4 = '$canal' AND p5 = '$regional'");
					if (count($valida_c) > 0) {
						$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 3 AND id_tipo = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
						if (count($valida_d) > 0) {
							$datos_distri[$c]['permiso'] = 1;
						} else {
							$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
							if (count($valida_d) > 0) {
								$datos_distri[$c]['permiso'] = 2;
							} else {
								$datos_distri[$c]['permiso'] = 3;
							}
						}
					} else {
						$datos_distri[$c]['permiso'] = 3;
					}
				}
			}
		}
		return $datos_distri;
	}

	public function Retornar_Arbol($cedula)
	{
		$sql = "select id from usuarios where cedula = $cedula";
		$redus = $this->DB->devolver_array($sql);
		$id = $redus[0]['id'];

		$con1 = "SELECT id,nombre,descripcion,estado FROM regional";
		$con2 = "SELECT id,nombre,estado FROM canal WHERE distri = 1";
		$con3 = "SELECT id, id_canal,nombre,estado FROM distribuidores";
		$con4 = "SELECT id,id_reg,id_distri FROM regionales_distri";
		$con5 = "SELECT idpos FROM {$GLOBALS["BD_POS"]}.puntos";
		$con6 = "SELECT id,id_regional,id_canal FROM canal_regional";

		$datos_regional = $this->DB->devolver_array($con1);
		$datos_canal = $this->DB->devolver_array($con2);
		$datos_distri = $this->DB->devolver_array($con3);
		$datos_reg_distri = $this->DB->devolver_array($con4);
		$dastos_pos = $this->DB->devolver_array($con5);
		$datos_can_reg = $this->DB->devolver_array($con6);
		$datos = array();

		//Realizar Consultas de los datos para saber si tiene permisos

		//Consultar Permisos de la Regional.
		for ($r = 0; $r < count($datos_regional); $r++) {
			$id_regional = $datos_regional[$r]['id'];
			$valida_r = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id' AND tipo = 1 AND id_tipo = '$id_regional'");
			if (count($valida_r) > 0) {
				$datos[$id_regional] = $datos_distri;
				$datos_regional[$r]['permiso'] = 1;
			} else {
				$valida_r = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id' AND p5 = '$id_regional'");
				if (count($valida_r) > 0) {
					$datos_regional[$r]['permiso'] = 2;

					#//Consultar Permisos Canales
					for ($c = 0; $c < count($datos_canal); $c++) {
						$id_canal = $datos_canal[$c]['id'];
						$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id' AND tipo = 2 AND id_tipo = '$id_canal' AND p5 = '$id_regional'");
						if (count($valida_c) > 0) {
							$datos[$id_regional] = $datos_distri;
							$datos_canal[$c]['permiso'] = 1;
						} else {
							$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id'  AND p4 = '$id_canal' AND p5 = '$id_regional'");
							if (count($valida_c) > 0) {
								$datos_canal[$c]['permiso'] = 2;
								#//Consultar Permisos Distribuidores
								for ($d = 0; $d < count($datos_distri); $d++) {
									$id_distri = $datos_distri[$d]['id'];
									$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id' AND tipo = 3 AND id_tipo = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional'");
									if (count($valida_d) > 0) {
										$datos_distri[$d]['permiso'] = 1;
									} else {
										$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional'");
										if (count($valida_d) > 0) {
											$datos_distri[$d]['permiso'] = 2;
										} else {
											$datos_distri[$d]['permiso'] = 3;
										}
									}
									$datos[$id_regional][] = $datos_distri[$d];
								}
								#//Fin Permisos Distribuidores
								//$datos[$id_regional] = $datos_distri;
							} else {
								$datos[$id_regional] = $datos_distri;
								$datos_canal[$c]['permiso'] = 3;
							}
						}
					}
					#//Fin Permisos Canal

				} else {
					$datos[$id_regional] = $datos_distri;
					$datos_regional[$r]['permiso'] = 3;
				}
			}
		}
		#//Fin Permisos Regional

		$datos = array(
			'' => $id,
			'datos_regional' => $datos_regional,
			'datos_canal' => $datos_canal,
			'datos_distri' => $datos_distri,
			'datos_reg_distri' => $datos_reg_distri,
			'dastos_pos' => $dastos_pos,
			'datos_can_reg' => $datos_can_reg,
			'otro' => $datos
		);
		return $datos;
	}

	public function mostrar_zonas($regional, $canal, $id_distri, $territorio, $cedula, $id)
	{


		$id_usu = $this->retornar_id($cedula, $id);

		$consulta = "SELECT id,descripcion as nombre FROM zonas WHERE territorio = '$territorio'";
		$datos_zonas = $this->DB->devolver_array($consulta);

		$valida_regional = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 1 AND id_tipo = '$regional'");
		if (count($valida_regional) > 0) {
			for ($c = 0; $c < count($datos_zonas); $c++) {
				$datos_zonas[$c]['permiso'] = 1;
			}
		} else {
			$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 2 AND id_tipo = '$canal' AND p5 = '$regional'");
			if (count($valida_c) > 0) {
				for ($c = 0; $c < count($datos_zonas); $c++) {
					$datos_zonas[$c]['permiso'] = 1;
				}
			} else {
				$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p4 = '$canal' AND p5 = '$regional'");
				if (count($valida_c) > 0) {
					$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 3 AND id_tipo = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
					if (count($valida_d) > 0) {
						for ($c = 0; $c < count($datos_zonas); $c++) {
							$datos_zonas[$c]['permiso'] = 1;
						}
					} else {
						$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
						if (count($valida_d) > 0) {

							$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 4 AND id_tipo = '$territorio' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
							if (count($valida_d) > 0) {
								for ($c = 0; $c < count($datos_zonas); $c++) {
									$datos_zonas[$c]['permiso'] = 1;
								}
							} else {

								for ($c = 0; $c < count($datos_zonas); $c++) {
									$id_zona = $datos_zonas[$c]['id'];
									$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' and tipo = 5 AND id_tipo = '$id_zona' AND p2 = '$territorio' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
									if (count($valida_d) > 0) {
										$datos_zonas[$c]['permiso'] = 1;
									} else {
										$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p1 = '$id_zona' AND p2 = '$territorio' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
										if (count($valida_d) > 0) {
											$datos_zonas[$c]['permiso'] = 2;
										} else {
											$datos_zonas[$c]['permiso'] = 3;
										}
									}
								}
							}
						} else {
							for ($c = 0; $c < count($datos_zonas); $c++) {
								$datos_zonas[$c]['permiso'] = 3;
							}
						}
					}
				} else {
					for ($c = 0; $c < count($datos_zonas); $c++) {
						$datos_zonas[$c]['permiso'] = 3;
					}
				}
			}
		}
		return $datos_zonas;
	}

	public function mostrar_territorios($regional, $canal, $id_distri, $cedula, $id)
	{

		$id_usu = $this->retornar_id($cedula, $id);

		$consulta = "SELECT t.id,t.descripcion FROM territorios t, territorios_distribuidor td
				 WHERE t.id = td.id_territorio AND td.id_distri = '$id_distri' AND td.id_regional = '$regional'";
		$datos_territorios = $this->DB->devolver_array($consulta);
		//Consulta Permiso Regional.

		$valida_regional = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 1 AND id_tipo = '$regional'");
		if (count($valida_regional) > 0) {
			for ($c = 0; $c < count($datos_territorios); $c++) {
				$datos_territorios[$c]['permiso'] = 1;
			}
		} else {
			$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 2 AND id_tipo = '$canal' AND p5 = '$regional'");
			if (count($valida_c) > 0) {
				for ($c = 0; $c < count($datos_territorios); $c++) {
					$datos_territorios[$c]['permiso'] = 1;
				}
			} else {
				$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p4 = '$canal' AND p5 = '$regional'");
				if (count($valida_c) > 0) {
					$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 3 AND id_tipo = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
					if (count($valida_d) > 0) {
						for ($c = 0; $c < count($datos_territorios); $c++) {
							$datos_territorios[$c]['permiso'] = 1;
						}
					} else {
						$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
						if (count($valida_d) > 0) {
							for ($c = 0; $c < count($datos_territorios); $c++) {
								$id_territorio = $datos_territorios[$c]['id'];
								$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 4 AND id_tipo = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
								if (count($valida_d) > 0) {
									$datos_territorios[$c]['permiso'] = 1;
								} else {
									$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$canal' AND p5 = '$regional'");
									if (count($valida_d) > 0) {
										$datos_territorios[$c]['permiso'] = 2;
									} else {
										$datos_territorios[$c]['permiso'] = 3;
									}
								}
							}
						} else {
							for ($c = 0; $c < count($datos_territorios); $c++) {
								$datos_territorios[$c]['permiso'] = 3;
							}
						}
					}
				} else {
					for ($c = 0; $c < count($datos_territorios); $c++) {
						$datos_territorios[$c]['permiso'] = 3;
					}
				}
			}
		}

		return $datos_territorios;
	}

	public function Mostrar_Puntos($regional, $canal, $distri, $territorio, $zona, $cedula, $id)
	{
		$id_usu = $this->retornar_id($cedula, $id);

		$consulta = "SELECT idpos, razon,cedula,nombre_cli,zona,territorio 
				FROM {$GLOBALS["BD_POS"]}.puntos WHERE zona = '$zona' AND territorio = '$territorio'";
		$datos_puntos = $this->DB->devolver_array($consulta);

		for ($c = 0; $c < count($datos_puntos); $c++) {
			$datos_puntos[$c]['razon'] = utf8_encode($datos_puntos[$c]['razon']);
			$datos_puntos[$c][1] = utf8_encode($datos_puntos[$c]['razon']);
			$datos_puntos[$c]['nombre_cli'] = utf8_encode($datos_puntos[$c]['nombre_cli']);
			$datos_puntos[$c][3] = utf8_encode($datos_puntos[$c]['nombre_cli']);
		}

		$valida_regional = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 1 AND id_tipo = '$regional'");
		if (count($valida_regional) > 0) {
			for ($c = 0; $c < count($datos_puntos); $c++) {
				$datos_puntos[$c]['permiso'] = 1;
			}
		} else {
			$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 2 AND id_tipo = '$canal' AND p5 = '$regional'");
			if (count($valida_c) > 0) {
				for ($c = 0; $c < count($datos_puntos); $c++) {
					$datos_puntos[$c]['permiso'] = 1;
				}
			} else {
				$valida_c = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p4 = '$canal' AND p5 = '$regional'");
				if (count($valida_c) > 0) {
					$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 3 AND id_tipo = '$distri' AND p4 = '$canal' AND p5 = '$regional'");
					if (count($valida_d) > 0) {
						for ($c = 0; $c < count($datos_puntos); $c++) {
							$datos_puntos[$c]['permiso'] = 1;
						}
					} else {
						$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND p3 = '$distri' AND p4 = '$canal' AND p5 = '$regional'");
						if (count($valida_d) > 0) {

							$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' AND tipo = 4 AND id_tipo = '$territorio' AND p3 = '$distri' AND p4 = '$canal' AND p5 = '$regional'");
							if (count($valida_d) > 0) {
								for ($c = 0; $c < count($datos_puntos); $c++) {
									$datos_puntos[$c]['permiso'] = 1;
								}
							} else {

								$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' and tipo = 5 AND id_tipo = '$zona' AND p2 = '$territorio' AND p3 = '$distri' AND p4 = '$canal' AND p5 = '$regional'");
								if (count($valida_d) > 0) {
									for ($c = 0; $c < count($datos_puntos); $c++) {
										$datos_puntos[$c]['permiso'] = 1;
									}
								} else {
									for ($c = 0; $c < count($datos_puntos); $c++) {
										$id_punto = $datos_puntos[$c]['idpos'];
										$valida_d = $this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE id_usus = '$id_usu' and tipo = 6 AND id_tipo = '$id_punto' AND p1 = '$zona' AND p2 = '$territorio' AND p3 = '$distri' AND p4 = '$canal' AND p5 = '$regional'");
										if (count($valida_d) > 0) {
											$datos_puntos[$c]['permiso'] = 1;
										} else {
											$datos_puntos[$c]['permiso'] = 3;
										}
									}
								}
							}
						} else {
							for ($c = 0; $c < count($datos_puntos); $c++) {
								$datos_puntos[$c]['permiso'] = 3;
							}
						}
					}
				} else {
					for ($c = 0; $c < count($datos_puntos); $c++) {
						$datos_puntos[$c]['permiso'] = 3;
					}
				}
			}
		}


		return $datos_puntos;
	}
	// Permisos Regionales, Canales, Distri
	public function Guardar_Permisos($tipo, $id, $cedula, $usuario) // Regionales // Canales // Distribuidores
	{
		$res = 0;
		$id_usu = $this->retornar_id($cedula, $usuario);

		$this->DB->consultar("BEGIN");
		if ($tipo == 1) // Para Guardar Permisos de la regional
		{
			//Borrar los datos de los que esten bajo en arbol.
			$delete = "DELETE FROM niveles_dcs WHERE p5 = $id AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(0, 0, '', '', '', '', $id, 2, $cedula, $usuario); // Borro todo debajo de esta regional
				}
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 1 AND id_tipo = $id AND id_usus = '$id_usu'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(1, $id, '', '', '', '', '', 2, $cedula, $usuario); // Borro la regional
					}
					$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus) VALUES (1,'$id','$id_usu')";
					if ($this->DB->consultar($insert)) {
						$this->Guardar_Auditoria(1, $id, '', '', '', '', '', 1, $cedula, $usuario); // Inserto la regional
						$res = array("res" => 1, "msg" => "Se asignaron los permisos con exito a la regional");
						$this->DB->consultar("COMMIT");
					} else {
						$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos 1");
						$this->DB->consultar("ROLLBACK");
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos 2");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos 3");
				$this->DB->consultar("ROLLBACK");
			}
		} // FIn Guardar por Regional.!

		else if ($tipo == 2) // Para Guardar Permisos del Canal.!
		{
			//explode de $id..
			$id = explode("_", $id);
			$id_regional = $id[0];
			$id_canal = $id[1];
			//Borrar los datos del arbol hacia abajo.!
			$delete = "DELETE FROM niveles_dcs WHERE p5 = '$id_regional' AND p4 = '$id_canal' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(0, 0, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario); // Borro todo sobre regional y canal.!
				}
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_tipo = '$id_canal' AND id_usus = '$id_usu' AND p5 = '$id_regional'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 2, $cedula, $usuario); // Borro todo sobre regional y canal.!
					}
					$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5) VALUES (2,'$id_canal','$id_usu','$id_regional')";
					if ($this->DB->consultar($insert)) {
						$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 1, $cedula, $usuario); // Insertar canal.!

						$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 2 AND id_usus = '$id_usu' AND p5 = '$id_regional'"));
						$con_canales = count($this->DB->devolver_array("SELECT id FROM canal_regional WHERE id_regional = '$id_regional' AND id_canal = 1"));
						if ($con_canales == $con_niveles) {
							$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_usus = '$id_usu' AND p5 = '$id_regional'";
							if ($this->DB->consultar($delete)) {
								if ($this->DB->rows_affect() > 0) {
									$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 2, $cedula, $usuario); // Borro todo sobre regional y canal.!
								}
								$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus) VALUES (1,'$id_regional','$id_usu')";
								if ($this->DB->consultar($insert)) {
									$this->Guardar_Auditoria(1, $id_regional, '', '', '', '', '', 1, $cedula, $usuario); // Insertar Regional.!
									$res = array("res" => 1, "msg" => "Se asignaron los permisos con exito a la regional");
									$this->DB->consultar("COMMIT");
								} else {
									$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al canal.");
									$this->DB->consultar("ROLLBACK");
								}
							} else {
								$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al canal.");
								$this->DB->consultar("ROLLBACK");
							}
						} else {
							$res = array("res" => 1, "msg" => "Se asignaron los permisos con exito al canal");
							$this->DB->consultar("COMMIT");
						}
					} else {
						$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al canal.");
						$this->DB->consultar("ROLLBACK");
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al canal.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al canal.");
				$this->DB->consultar("ROLLBACK");
			}
		} // Fin de Guardar Por canal.!

		// Guardar Datos Por Distribuidor.!
		else if ($tipo == 3) {
			//explode de $id..
			$id = explode("_", $id);
			$id_regional = $id[0];
			$id_canal = $id[1];
			$id_distri = $id[2];
			$delete = "DELETE FROM niveles_dcs WHERE p5 = '$id_regional' AND p4 = '$id_canal' AND p3 = '$id_distri' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(0, 0, '', '', $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario); // Borro todo sobre regional, canal y distribuidor.!
				}
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 3 AND id_tipo = '$id_distri' AND id_usus = '$id_usu' AND p5 = '$id_regional' AND p4 = '$id_canal'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(3, $id_distri, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario); // Borro todo sobre el distribuidor.!
					}
					$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4) VALUES (3,'$id_distri','$id_usu','$id_regional','$id_canal')";
					if ($this->DB->consultar($insert)) {
						$this->Guardar_Auditoria(3, $id_distri, '', '', '', $id_canal, $id_regional, 1, $cedula, $usuario); // Insertar Distribuidor.!
						$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 3 AND id_usus = '$id_usu' AND p5 = '$id_regional' AND p4 = '$id_canal'"));
						$con_distri = count($this->DB->devolver_array("SELECT d.id FROM distribuidores d
																	   INNER JOIN regionales_distri rd ON rd.id_distri = d.id AND rd.id_reg = '$id_regional'
																	   WHERE d.id_canal = '$id_canal' "));
						if ($con_niveles == $con_distri) {
							$delete = "DELETE FROM niveles_dcs WHERE tipo = 3  AND p5 = '$id_regional' AND p4 = '$id_canal'  AND id_usus = '$id_usu'";
							if ($this->DB->consultar($delete)) {
								if ($this->DB->rows_affect() > 0) {
									$this->Guardar_Auditoria(3, $id_distri, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario); // Borro todo sobre el distribuidor.!
								}
								$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5) VALUES (2,'$id_canal','$id_usu','$id_regional')";
								if ($this->DB->consultar($insert)) {
									$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 1, $cedula, $usuario); // Insertar Canal.!
									$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 2 AND id_usus = '$id_usu' AND p5 = '$id_regional'"));
									$con_canales = count($this->DB->devolver_array("SELECT id FROM canal_regional WHERE id_regional = '$id_regional' AND id_canal = 1"));
									if ($con_canales == $con_niveles) {
										$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_usus = '$id_usu' AND p5 = '$id_regional'";
										if ($this->DB->consultar($delete)) {
											if ($this->DB->rows_affect() > 0) {
												$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 2, $cedula, $usuario); // Borro todo sobre el canal.!
											}
											$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus) VALUES (1,'$id_regional','$id_usu')";
											if ($this->DB->consultar($insert)) {
												$this->Guardar_Auditoria(1, $id_regional, '', '', '', '', '', 1, $cedula, $usuario); // Insertar Regional.!
												$res = array("res" => 1, "msg" => "Se asignaron los permisos con exito a el distribuidor");
												$this->DB->consultar("COMMIT");
											} else {
												$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos a el distribuidor.");
												$this->DB->consultar("ROLLBACK");
											}
										}
									} else {
										$res = array("res" => 1, "msg" => "Se asignaron los permisos con exito a el distribuidor");
										$this->DB->consultar("COMMIT");
									}
								} else {
									$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al distribuidor.");
									$this->DB->consultar("ROLLBACK");
								}
							} else {
								$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al distribuidor.");
								$this->DB->consultar("ROLLBACK");
							}
						} else {
							$res = array("res" => 1, "msg" => "Se asignaron los permisos con exito al canal");
							$this->DB->consultar("COMMIT");
						}
					} else {
						$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al distribuidor.");
						$this->DB->consultar("ROLLBACK");
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al distribuidor.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar Asignar los permisos al distribuidor.");
				$this->DB->consultar("ROLLBACK");
			}
		}

		return $res;
	}

	public function Borrar_Permisos($tipo, $id, $cedula, $usuario) // Regionales // Canales // Distribuidores
	{
		$res = 0;
		$id_usu = $this->retornar_id($cedula, $usuario);
		$this->DB->consultar("BEGIN");
		if ($tipo == 1) // Consulta para eliminar por Regional.
		{
			//Borrar los datos que se encuentren debajo en el arbol
			$delete = "DELETE FROM niveles_dcs  WHERE p5 = $id AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(0, 0, '', '', '', '', $id, 2, $cedula, $usuario); // Borro todo sobre la regional.!
				}
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 1 AND id_tipo = '$id' AND id_usus = '$id_usu'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(1, $id, '', '', '', '', '', 2, $cedula, $usuario); // Borro la regional.!
					}
					$res = array("res" => 1, "msg" => "Se eliminaron los permisos a la regional.");
					$this->DB->consultar("COMMIT");
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la regional.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la regional.");
				$this->DB->consultar("ROLLBACK");
			}
		}
		//Final tipo 1: Regional.!

		else if ($tipo == 2) { // Consulta para eliminar por Canal.

			$id = explode("_", $id);
			$id_regional = $id[0];
			$id_canal = $id[1];

			$delete = "DELETE FROM niveles_dcs WHERE p5 = '$id_regional' AND p4 = '$id_canal' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(0, 0, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario); // Borro todo sobre la regional y canal!
				}
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_tipo = '$id_canal' AND id_usus = '$id_usu' AND p5 = '$id_regional'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 2, $cedula, $usuario); // Borro el canal!
					}
					$con_regional = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 1 AND id_tipo = '$id_regional' AND id_usus = '$id_usu'"));
					if ($con_regional > 0) {
						$delete = "DELETE FROM niveles_dcs WHERE tipo = 1 AND id_tipo = '$id_regional' AND id_usus = '$id_usu'";
						$this->DB->consultar($delete);
						$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_usus = '$id_usu' AND p5 = '$id_regional'";
						$this->DB->consultar($delete);
						if ($this->DB->rows_affect() > 0) {
							$this->Guardar_Auditoria(1, $id_regional, '', '', '', '', '', 2, $cedula, $usuario); // Borro la Regional!
						}
						/*$consultar_canales = $this->DB->devolver_array("SELECT id_canal FROM canal_regional WHERE id_regional = '$id_regional' and id_canal != '$id_canal' and id_canal = 1");
						$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5) VALUES ";
						for ($i=0; $i < count($consultar_canales); $i++) { 
							$insert .= "(2,'".$consultar_canales[$i]['id_canal']."','$id_usu','$id_regional'),";
						}
						$insert = substr($insert, 0, -1);*/
						if ($this->DB->consultar($delete)) {
							$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el canal.");
							$this->DB->consultar("COMMIT");
						} else {
							$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el canal.");
							$this->DB->consultar("ROLLBACK");
						}
					} else {
						// ------- ******** ------- //
						$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el canal.");
						$this->DB->consultar("COMMIT");
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el canal.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el canal.");
				$this->DB->consultar("ROLLBACK");
			}
		}
		//Final tipo 2: Canal.!

		if ($tipo == 3) { // Consulta para eliminar por Distribuidor.!
			$id = explode("_", $id);
			$id_regional = $id[0];
			$id_canal = $id[1];
			$id_distri = $id[2];

			$delete = "DELETE FROM niveles_dcs WHERE p5 = '$id_regional' AND p4 = '$id_canal' AND p3 = '$id_distri' AND  id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(0, 0, '', '', $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario); // Borro todo sobre distri, canal y regional!
				}
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 3 AND id_tipo = '$id_distri' AND id_usus = '$id_usu' AND p5 = '$id_regional' AND p4 = '$id_canal'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(3, $id_distri, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario); // Borro el distri!
					}
					$con_regional = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 1 AND id_tipo = '$id_regional' AND id_usus = '$id_usu'"));
					if ($con_regional > 0) {
						$delete = "DELETE FROM niveles_dcs WHERE tipo = 1 AND id_tipo = '$id_regional' AND id_usus = '$id_usu'";
						$this->DB->consultar($delete);
						$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_tipo = '$id_canal' AND id_usus = '$id_usu' and p5 = '$id_regional'";
						$this->DB->consultar($delete);
						if ($this->DB->rows_affect() > 0) {
							$this->Guardar_Auditoria(1, $id_regional, '', '', '', '', '', 2, $cedula, $usuario); // Borro la regional!
						}
						$consultar_distri = $this->DB->devolver_array("SELECT d.id FROM distribuidores d
																	   INNER JOIN regionales_distri rd ON rd.id_distri = d.id AND rd.id_reg = '$id_regional'
																	   WHERE d.id_canal = '$id_canal' AND d.id != '$id_distri'");
						$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4) VALUES ";
						for ($i = 0; $i < count($consultar_distri); $i++) {
							$insert .= "(3,'" . $consultar_distri[$i]['id'] . "','$id_usu','$id_regional','$id_canal'),";
						}
						if (count($consultar_distri) > 0) {
							$insert = substr($insert, 0, -1);
							if ($this->DB->consultar($insert)) {
								for ($i = 0; $i < count($consultar_distri); $i++) {
									$this->Guardar_Auditoria(3, $consultar_distri[$i]['id'], '', '', '', $id_canal, $id_regional, 1, $cedula, $usuario);
								}
								$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el distribuidor.");
								$this->DB->consultar("COMMIT");
							} else {
								$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el distribuidor.");
								$this->DB->consultar("ROLLBACK");
							}
						} else {
							$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el distribuidor.");
							$this->DB->consultar("COMMIT");
						}
					} else {
						$con_canal = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 2 AND id_tipo = '$id_canal' AND id_usus = '$id_usu' and p5 = '$id_regional'"));
						if ($con_canal > 0) {
							$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_tipo = '$id_canal' AND id_usus = '$id_usu' AND p5 = '$id_regional'";
							$this->DB->consultar($delete);
							if ($this->DB->rows_affect() > 0) {
								$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 2, $cedula, $usuario);
							}
							$consultar_distri = $this->DB->devolver_array("SELECT d.id FROM distribuidores d
																	   INNER JOIN regionales_distri rd ON rd.id_distri = d.id AND rd.id_reg = '$id_regional'
																	   WHERE d.id_canal = '$id_canal' AND d.id != '$id_distri'");
							$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4) VALUES ";
							for ($i = 0; $i < count($consultar_distri); $i++) {
								$insert .= "(3,'" . $consultar_distri[$i]['id'] . "','$id_usu','$id_regional','$id_canal'),";
							}
							$insert = substr($insert, 0, -1);
							if ($this->DB->consultar($insert)) {
								for ($i = 0; $i < count($consultar_distri); $i++) {
									$this->Guardar_Auditoria(3, $consultar_distri[$i]['id'], '', '', '', $id_canal, $id_regional, 1, $cedula, $usuario);
								}
								$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el distribuidor.");
								$this->DB->consultar("COMMIT");
							} else {
								$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el distribuidor.");
								$this->DB->consultar("ROLLBACK");
							}
						} else {
							$delete = "DELETE FROM niveles_dcs WHERE tipo = 3 AND id_tipo = '$id_distri' AND id_usus = '$id_usu' AND p5 = '$id_regional' AND p4 = '$id_canal'";
							//$this->DB->consultar($delete);
							if ($this->DB->consultar($delete)) {
								if ($this->DB->rows_affect() > 0) {
									$this->Guardar_Auditoria(3, $id_distri, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario);
								}
								$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el distribuidor.");
								$this->DB->consultar("COMMIT");
							} else {
								$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el distribuidor.");
								$this->DB->consultar("ROLLBACK");
							}
						}
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el distribuidor.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el distribuidor.");
				$this->DB->consultar("ROLLBACK");
			}
		}
		//Final tipo 3: Distribuidor.!
		return $res;
	}

	// Permisos Territorios.!
	public function Borrar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $user)
	{
		//Funcion para eliminar los permisos de un territorio.!
		$res = array();
		$id_usu = $this->retornar_id($cedula, $user);
		$usuario = $id_usu;
		$this->DB->consultar("BEGIN");
		$select = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 4 AND id_tipo = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
		if ($select > 0) {
			$delete = "DELETE FROM niveles_dcs WHERE tipo = 4 AND id_tipo = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(4, $id_territorio, '', '', $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario);
				}
				$res = array("res" => 1, "msg" => "Se eliminaron los permisos a la ruta.");
				$this->DB->consultar("COMMIT");
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta.");
				$this->DB->consultar("ROLLBACK");
			}
		} else {

			$delete = "DELETE FROM niveles_dcs WHERE tipo = 4 AND p5 = '$id_regional' AND p4 = '$id_canal' AND p3 = '$id_distri' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(4, 0, '', '', $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario);
				}

				$delete = "DELETE FROM niveles_dcs WHERE tipo = 3 AND id_tipo='$id_distri' AND p5 = '$id_regional' AND p4 = '$id_canal'  AND id_usus = '$id_usu'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(3, $id_distri, '', '', '', $id_canal, $id_regional, 2, $cedula, $usuario);
					}
					$delete = "DELETE FROM niveles_dcs WHERE tipo = 2 AND id_tipo = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'";
					if ($this->DB->consultar($delete)) {
						if ($this->DB->rows_affect() > 0) {
							$this->Guardar_Auditoria(2, $id_canal, '', '', '', '', $id_regional, 2, $cedula, $usuario);
						}
						$deletec = "DELETE FROM niveles_dcs WHERE tipo = 1 AND id_tipo = '$id_regional' AND id_usus = '$id_usu'";
						if ($this->DB->consultar($deletec)) {
							if ($this->DB->rows_affect() > 0) {
								$this->Guardar_Auditoria(1, $id_regional, '', '', '', '', '', 2, $cedula, $usuario);
							}
							$consulta = "SELECT t.id,t.descripcion FROM territorios t, territorios_distribuidor td
				 					 WHERE t.id = td.id_territorio AND td.id_distri = '$id_distri' AND td.id_regional = '$id_regional'";

							$datos_territorios = $this->DB->devolver_array($consulta);
							if (count($datos_territorios) > 1) {
								$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4,p3) VALUES ";
								for ($i = 0; $i < count($datos_territorios); $i++) {
									if ($datos_territorios[$i]['id'] != $id_territorio) {
										$insert .= "(4,'" . $datos_territorios[$i]['id'] . "','$id_usu','$id_regional','$id_canal','$id_distri'),";
									}
								}
								$insert = substr($insert, 0, -1);
								if ($this->DB->consultar($insert)) {
									for ($i = 0; $i < count($datos_territorios); $i++) {
										if ($datos_territorios[$i]['id'] != $id_territorio) {
											$this->Guardar_Auditoria(4, $datos_territorios[$i]['id'], '', '', $id_distri, $id_canal, $id_regional, 1, $cedula, $usuario);
										}
									}
									$datos_distri = $this->DB->devolver_array("SELECT d.id FROM distribuidores d
												 INNER JOIN regionales_distri rd ON rd.id_distri = d.id AND rd.id_reg = '$id_regional'
												 WHERE d.id_canal = '$id_canal' AND d.id != '$id_distri'");
									$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4) VALUES ";
									for ($i = 0; $i < count($datos_distri); $i++) {
										if ($datos_distri[$i]['id'] != $id_distri) {
											$insert .= "(3,'" . $datos_distri[$i]['id'] . "','$id_usu','$id_regional','$id_canal'),";
										}
									}
									$insert = substr($insert, 0, -1);
									if ($this->DB->consultar($insert)) {
										for ($i = 0; $i < count($datos_distri); $i++) {
											if ($datos_distri[$i]['id'] != $id_distri) {
												$this->Guardar_Auditoria(3, $datos_distri[$i]['id'], '', '', '', $id_canal, $id_regional, 1, $cedula, $usuario);
											}
										}
										$res = array("res" => 1, "msg" => "Se eliminaron los permisos a la ruta.");
										$this->DB->consultar("COMMIT");
									} else {
										$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta.");
										$this->DB->consultar("ROLLBACK");
									}
								} else {
									$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta.");
									$this->DB->consultar("ROLLBACK");
								}
							} else {
								$datos_distri = $this->DB->devolver_array("SELECT d.id FROM distribuidores d
												 INNER JOIN regionales_distri rd ON rd.id_distri = d.id AND rd.id_reg = '$id_regional'
												 WHERE d.id_canal = '$id_canal' AND d.id != '$id_distri'");
								if (count($datos_distri) > 1) {
									$d_distri = $id_regional . "_" . $id_canal . "_" . $id_distri;
									$res = $this->Borrar_Permisos(3, $d_distri, $cedula, $user);
								} else {
									$res = array("res" => 1, "msg" => "Se eliminaron los permisos a la ruta.");
									$this->DB->consultar("COMMIT");
								}
							}
						} else {
							$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta.");
							$this->DB->consultar("ROLLBACK");
						}
					} else {
						$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta.");
						$this->DB->consultar("ROLLBACK");
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta.");
					$this->DB->consultar("ROLLBACK");
				}
				/*$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el distribuidor.");
			$this->DB->consultar("COMMIT");*/
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a la ruta 8.");
				$this->DB->consultar("ROLLBACK");
			}
		}

		return $res;
	}
	public function Guardar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $usuario)
	{
		$res = array();
		$id_usu = $this->retornar_id($cedula, $usuario);

		$this->DB->consultar("BEGIN");

		$select = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 4 AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
		if ($select > 0) {
			$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p3,p4,p5) VALUES (4,'$id_territorio','$id_usu','$id_distri','$id_canal','$id_regional')";
			if ($this->DB->consultar($insert)) {
				$this->Guardar_Auditoria(4, $id_territorio, '', '', $id_distri, $id_canal, $id_regional, 1, $cedula, $usuario);

				$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 4 AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
				$con_terri_distri = count($this->DB->devolver_array("SELECT id FROM territorios_distribuidor WHERE id_distri = '$id_distri' AND id_regional = '$id_regional'"));
				if ($con_niveles == $con_terri_distri) {
					$d_distri = $id_regional . "_" . $id_canal . "_" . $id_distri;
					$res = $this->Guardar_Permisos(3, $d_distri, $cedula, $usuario);
				} else {
					$res = array("res" => 1, "msg" => "Se asignaron los permisos a la ruta.");
					$this->DB->consultar("COMMIT");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar asignar los permisos a la ruta.");
				$this->DB->consultar("ROLLBACK");
			}
		} else {
			$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p3,p4,p5) VALUES (4,'$id_territorio','$id_usu','$id_distri','$id_canal','$id_regional')";
			if ($this->DB->consultar($insert)) {
				$this->Guardar_Auditoria(4, $id_territorio, '', '', $id_distri, $id_canal, $id_regional, 1, $cedula, $usuario);

				$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 4 AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
				$con_terri_distri = count($this->DB->devolver_array("SELECT id FROM territorios_distribuidor WHERE id_distri = '$id_distri' AND id_regional = '$id_regional'"));
				if ($con_niveles == $con_terri_distri) {
					$d_distri = $id_regional . "_" . $id_canal . "_" . $id_distri;
					$res = $this->Guardar_Permisos(3, $d_distri, $cedula, $usuario);
				} else {
					$res = array("res" => 1, "msg" => "Se asignaron los permisos a la ruta.");
					$this->DB->consultar("COMMIT");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar asignar los permisos a la ruta.");
				$this->DB->consultar("ROLLBACK");
			}
		}

		return $res;
	}

	//Permisos Zonas ..!
	public function Borrar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario)
	{
		//die($cedula);
		$res = array();
		$id_usu = $this->retornar_id($cedula, $usuario);

		$this->DB->consultar("BEGIN");

		$con_niv = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 5 AND id_tipo = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
		if ($con_niv > 0) {
			$delete = "DELETE FROM niveles_dcs WHERE tipo = 5 AND id_tipo = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				if ($this->DB->rows_affect() > 0) {
					$this->Guardar_Auditoria(5, $zona, '', $id_territorio, $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario);
				}
				$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el circuito.");
				$this->DB->consultar("COMMIT");
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el circuito.");
				$this->DB->consultar("ROLLBACK");
			}
		} else {

			$con_niv = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 4 AND id_tipo = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
			if ($con_niv > 0) {
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 4 AND id_tipo = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'";
				if ($this->DB->consultar($delete)) {
					if ($this->DB->rows_affect() > 0) {
						$this->Guardar_Auditoria(4, $id_territorio, '', '', $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario);
					}
					$consulta = "SELECT id FROM zonas WHERE territorio = '$id_territorio' AND id != '$zona'";
					$datos_zonas = $this->DB->devolver_array($consulta);
					if (count($datos_zonas) > 1) {
						$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4,p3,p2) VALUES ";
						for ($i = 0; $i < count($datos_zonas); $i++) {
							if ($datos_zonas[$i]['id'] != $id_territorio) {
								$insert .= "(5,'" . $datos_zonas[$i]['id'] . "','$id_usu','$id_regional','$id_canal','$id_distri','$id_territorio'),";
							}
						}
						$insert = substr($insert, 0, -1);
						if ($this->DB->consultar($insert)) {
							for ($i = 0; $i < count($datos_zonas); $i++) {
								if ($datos_zonas[$i]['id'] != $id_territorio) {
									$this->Guardar_Auditoria(4, $datos_zonas[$i]['id'], '', $id_territorio, $id_distri, $id_canal, $id_regional, 1, $cedula, $usuario);
								}
							}

							$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el circuito.");
							$this->DB->consultar("COMMIT");
						} else {
							$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el circuito.");
							$this->DB->consultar("ROLLBACK");
						}
					} else {
						$res = $this->Borrar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $usuario);
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el circuito.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$consulta = "SELECT id FROM zonas WHERE territorio = '$id_territorio' AND id != '$zona'";
				$datos_zonas = $this->DB->devolver_array($consulta);
				if (count($datos_zonas) > 1) {
					$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4,p3,p2) VALUES ";
					for ($i = 0; $i < count($datos_zonas); $i++) {
						if ($datos_zonas[$i]['id'] != $id_territorio) {
							$insert .= "(5,'" . $datos_zonas[$i]['id'] . "','$id_usu','$id_regional','$id_canal','$id_distri','$id_territorio'),";
						}
					}
					$insert = substr($insert, 0, -1);
					if ($this->DB->consultar($insert)) {
						$res = $this->Borrar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $usuario);
					} else {
						$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el circuito.");
						$this->DB->consultar("ROLLBACK");
					}
				} else {
					$res = $this->Borrar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $usuario);
				}
			}
		}

		return $res;
	}

	public function Guardar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario)
	{
		$res = array();
		$id_usu = $this->retornar_id($cedula, $usuario);

		$this->DB->consultar("BEGIN");
		$select = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 5 AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
		if ($select > 0) {
			$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p2,p3,p4,p5) VALUES (5,'$zona','$id_usu','$id_territorio','$id_distri','$id_canal','$id_regional')";
			if ($this->DB->consultar($insert)) {
				$this->Guardar_Auditoria(5, $zona, '', $id_territorio, $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario);

				$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 5 AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
				$con_terr_zonas = count($this->DB->devolver_array("SELECT id FROM zonas WHERE territorio = '$id_territorio'"));
				if ($con_niveles == $con_terr_zonas) {
					$res = $this->Guardar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $usuario);
				} else {
					$res = array("res" => 1, "msg" => "Se asignaron los permisos a el circuito.");
					$this->DB->consultar("COMMIT");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar asignar los permisos a el circuito.");
				$this->DB->consultar("ROLLBACK");
			}
		} else {
			$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p2,p3,p4,p5) VALUES (5,'$zona','$id_usu','$id_territorio','$id_distri','$id_canal','$id_regional')";
			if ($this->DB->consultar($insert)) {
				$this->Guardar_Auditoria(5, $zona, '', $id_territorio, $id_distri, $id_canal, $id_regional, 2, $cedula, $usuario);
				$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 5 AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
				$con_terr_zonas = count($this->DB->devolver_array("SELECT id FROM zonas WHERE territorio = '$id_territorio'"));
				if ($con_niveles == $con_terr_zonas) {
					$res = $this->Guardar_Permisos_Territorio($id_regional, $id_canal, $id_distri, $id_territorio, $cedula, $usuario);
				} else {
					$res = array("res" => 1, "msg" => "Se asignaron los permisos a el circuito.");
					$this->DB->consultar("COMMIT");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar asignar los permisos a el circuito.");
				$this->DB->consultar("ROLLBACK");
			}
		}

		return $res;
	}

	//Permisos Puntos ..!
	public function Borrar_Permisos_Puntos($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $punto, $cedula, $usuario)
	{
		$res = array();
		$sql = "select id from usuarios where cedula = $cedula";
		$redus = $this->DB->devolver_array($sql);
		$id_usu = $redus[0]['id'];

		$this->DB->consultar("BEGIN");

		$con_niv = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 6 AND id_tipo = '$punto' AND p1 = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
		if ($con_niv > 0) {
			$delete = "DELETE FROM niveles_dcs WHERE tipo = 6 AND id_tipo = '$punto' AND p1 = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'";
			if ($this->DB->consultar($delete)) {
				$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el punto.");
				$this->DB->consultar("COMMIT");
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el punto.");
				$this->DB->consultar("ROLLBACK");
			}
		} else {
			$con_niv = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 5 AND id_tipo = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
			if ($con_niv > 0) {
				$delete = "DELETE FROM niveles_dcs WHERE tipo = 5 AND id_tipo = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4 = '$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'";
				if ($this->DB->consultar($delete)) {
					$consulta = "SELECT idpos FROM {$GLOBALS["BD_POS"]}.puntos WHERE zona = '$zona' AND territorio = '$id_territorio'";
					$datos_puntos = $this->DB->devolver_array($consulta);
					if (count($datos_puntos) > 1) {
						$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4,p3,p2,p1) VALUES ";
						for ($i = 0; $i < count($datos_puntos); $i++) {
							if ($datos_puntos[$i]['idpos'] != $punto) {
								$insert .= "(6,'" . $datos_puntos[$i]['idpos'] . "','$id_usu','$id_regional','$id_canal','$id_distri','$id_territorio','$zona'),";
							}
						}
						$insert = substr($insert, 0, -1);
						if ($this->DB->consultar($insert)) {
							$res = array("res" => 1, "msg" => "Se eliminaron los permisos a el punto.");
							$this->DB->consultar("COMMIT");
						} else {
							$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el punto.");
							$this->DB->consultar("ROLLBACK");
						}
					} else {
						$res = $this->Borrar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario);
					}
				} else {
					$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el punto.");
					$this->DB->consultar("ROLLBACK");
				}
			} else {
				$consulta = "SELECT idpos FROM {$GLOBALS["BD_POS"]}.puntos WHERE zona = '$zona' AND territorio = '$id_territorio' AND idpos != '$punto'";
				$datos_puntos = $this->DB->devolver_array($consulta);
				if (count($datos_puntos) > 1) {
					$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p5,p4,p3,p2,p1) VALUES ";
					for ($i = 0; $i < count($datos_puntos); $i++) {
						if ($datos_puntos[$i]['idpos'] != $punto) {
							$insert .= "(6,'" . $datos_puntos[$i]['idpos'] . "','$id_usu','$id_regional','$id_canal','$id_distri','$id_territorio','$zona'),";
						}
					}
					$insert = substr($insert, 0, -1);
					if ($this->DB->consultar($insert)) {
						$res = $this->Borrar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario);
					} else {
						$res = array("res" => 0, "msg" => "Error al intentar eliminar los permisos a el punto.");
						$this->DB->consultar("ROLLBACK");
					}
				} else {
					$res = $this->Borrar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario);
				}
			}
		}

		return $res;
	}

	public function Guardar_Permisos_Puntos($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $punto, $cedula, $usuario)
	{
		$res = array();
		$id_usu = $this->retornar_id($cedula, $usuario);

		$this->DB->consultar("BEGIN");
		$select = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 6 AND p1 = '$zona' AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
		if ($select > 0) {
			$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p1,p2,p3,p4,p5) VALUES (6,'$punto','$id_usu','$zona','$id_territorio','$id_distri','$id_canal','$id_regional')";
			if ($this->DB->consultar($insert)) {
				$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 6  AND p1 = '$zona'  AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
				$con_puntos = count($this->DB->devolver_array("SELECT idpos FROM {$GLOBALS["BD_POS"]}.puntos WHERE territorio = '$id_territorio' AND zona = '$zona'"));
				if ($con_niveles == $con_puntos) {
					$res = $this->Guardar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario);
				} else {
					$res = array("res" => 1, "msg" => "Se asignaron los permisos a el punto.");
					$this->DB->consultar("COMMIT");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar asignar los permisos a el punto.");
				$this->DB->consultar("ROLLBACK");
			}
		} else {
			$insert = "INSERT INTO niveles_dcs (tipo,id_tipo,id_usus,p1,p2,p3,p4,p5) VALUES (6,'$punto','$id_usu','$zona','$id_territorio','$id_distri','$id_canal','$id_regional')";
			if ($this->DB->consultar($insert)) {
				$con_niveles = count($this->DB->devolver_array("SELECT id FROM niveles_dcs WHERE tipo = 6 AND p1 = '$zona'  AND p2 = '$id_territorio' AND p3 = '$id_distri' AND p4='$id_canal' AND p5 = '$id_regional' AND id_usus = '$id_usu'"));
				$con_puntos = count($this->DB->devolver_array("SELECT idpos FROM {$GLOBALS["BD_POS"]}.puntos WHERE territorio = '$id_territorio' AND zona = '$zona'"));
				if ($con_niveles == $con_puntos) {

					$res = $this->Guardar_Permisos_Zonas($id_regional, $id_canal, $id_distri, $id_territorio, $zona, $cedula, $usuario);
				} else {
					$res = array("res" => 1, "msg" => "Se asignaron los permisos a el punto.");
					$this->DB->consultar("COMMIT");
				}
			} else {
				$res = array("res" => 0, "msg" => "Error al intentar asignar los permisos a el punto.");
				$this->DB->consultar("ROLLBACK");
			}
		}

		return $res;
	}

	public function Retornar_Usuario($cedula, $usuario)
	{
		$id = $this->retornar_id($cedula, $usuario);
		$consulta = "SELECT concat(nombre,' ',apellido) nombre_usuario, cedula FROM usuarios WHERE id = '$id'";
		return $this->DB->devolver_array($consulta);
	}
}




// Fin clase
