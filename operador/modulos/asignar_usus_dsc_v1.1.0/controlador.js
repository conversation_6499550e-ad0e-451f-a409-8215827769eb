var tipo_ac = 0;
var dtablet;
var dtablez;
var dtablep;
var dtabler;
var dtablec;
var dtabled;
var niveles;
var todos = 0;
var id_usu = 0;
var cedula = 0;
var usuario = 0;

var alia_dep;
var alia_ciudad;
var alia_regionales;
var alia_territorio;
var alia_zona;

const URL_MODULO = "modulos/asignar_usus_dsc_v1.1.0/";

const handleAlias = () => {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "cargar_campos"
    })
  }).then(response => response.json()).then(data => {
    data.map(({ tipo, campo, nombre }) => {
      if (tipo === "0" && campo == "1") return (alia_dep = nombre);
      if (tipo === "0" && campo == "2") return (alia_ciudad = nombre);
      if (tipo === "1" && campo == "1") return (alia_regionales = nombre);
      if (tipo === "1" && campo == "2") return (alia_territorio = nombre);
      if (tipo === "1" && campo == "3") return (alia_zona = nombre);
    });

    setAlias();
  }).catch(error => {
    console.error('Error:', error);
  });
};

const setAlias = () => {
  $(".a_departamento").html(alia_dep);
  $(".a_ciudad").html(alia_ciudad);
  $(".a_regional").html(alia_regionales);
  $(".a_territorio").html(alia_territorio);
  $(".a_zona").html(alia_zona);
};

$(document).ready(function () {
  handleAlias();
  funcion_cargar_usuarios("usuariob");
  $("#frm_user").submit(function () {
    event.preventDefault();
    cedula = $("#cedulab").val();
    usuario = $("#usuariob").val();

    if (cedula == "" && usuario == "") {
      Notificacion("Selecciona un usuario o ingresa la cedula");
      $("#info_usu, #resultado").hide();
      return;
    }
    Mostrar_Regionales();
    $("#info_usu, #resultado").hide();
    Mostrar_Usuario();
  });
  $("#v_circuitos").click(function () {
    $("#territorios").show();
    $("#zonas").hide();
    $("#puntos").hide();
  });
  $("#v_rutas").click(function () {
    $("#territorios").hide();
    $("#zonas").show();
    $("#puntos").hide();
  });
  $(".volver").click(function () {
    if ($(this).attr("data-ant") == "regionales") {
      Mostrar_Regionales();
    }
    if ($(this).attr("data-ant") == "canales") {
      Mostrar_Canales($(this).attr("data-regional"));
    }
    if ($(this).attr("data-ant") == "distribuidor") {
      Mostrar_Distribuidores(
        $(this).attr("data-canal"),
        $(this).attr("data-regional")
      );
    }
    if ($(this).attr("data-ant") == "circuitos") {
      Mostrar_Territorios(
        $(this).attr("data-canal"),
        $(this).attr("data-regional"),
        $(this).attr("data-distri")
      );
    }
    if ($(this).attr("data-ant") == "rutas") {
      Mostrar_Zonas(
        $(this).attr("data-regional"),
        $(this).attr("data-canal"),
        $(this).attr("data-distri"),
        $(this).attr("data-territorio")
      );
    }
  });
});

function Mostrar_Regionales() {
  $("#info_usu, #resultado").show();
  $("#titulo").html(alia_regionales || "REGIONALES");
  if (!$.fn.DataTable.isDataTable("#t_regionales")) {
    dtabler = $("#t_regionales").DataTable({
      ajax: {
        url: URL_MODULO + "controlador.php",
        type: "POST",
        deferRender: false,
        data: {
          accion: "mostrar_regionales",
          cedula: cedula,
          usuario: usuario,
        },
      },
      bFilter: false,
      responsive: true,
      columns: [
        { data: "id", width: "10" },
        { data: "nombre", width: "70" },
        { data: "id", width: "20" },
      ],
      columnDefs: [
        {
          targets: 0,
          data: "id",
          render: function (data, type, row) {
            if (row["permiso"] == 1) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_regional" checked/>'
              );
            } else if (row["permiso"] == 2) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_regional" />'
              );
            } else {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_regional"/>'
              );
            }
          },
        },
        {
          targets: 2,
          data: "",
          render: function (data, type, row) {
            return '<button class="btn btn-sm btn-primary v_canal"><i class="glyphicon glyphicon-plus"></i></button>';
          },
        },
      ],
      fnDrawCallback: function () {
        $("input[type=checkbox][data-permiso=2]").prop("indeterminate", true);
        $(".chk_regional").unbind("click");
        $(".v_canal").unbind("click");

        $(".chk_regional").click(function () {
          var data = dtabler.row($(this).parents("tr")).data();
          control = $(this);
          if ($(this).is(":checked")) {
            BootstrapDialog.confirm(
              "¿Esta seguro de asignar el permiso?",
              function (result) {
                if (result) {
                  Guardar_Permisos(1, data["id"]);
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", false);
                  }
                }
              }
            );
          } else {
            BootstrapDialog.confirm(
              "¿Esta seguro de eliminar el permiso?",
              function (result) {
                if (result) {
                  Borrar_Permisos(1, data["id"]);
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", true);
                  }
                }
              }
            );
          }
        });

        $(".v_canal").click(function () {
          var datar = dtabler.row($(this).parents("tr")).data();
          Mostrar_Canales(datar["id"]);
        });
      },
    });
  } else {
    dtabler.destroy();
    Mostrar_Regionales();
  }
  Mostrar_tabla("regionales");
}

function Mostrar_Canales(regional) {
  $("#info_usu, #resultado").show();
  $("#titulo").html("CANALES");
  if (!$.fn.DataTable.isDataTable("#t_canales")) {
    dtablec = $("#t_canales").DataTable({
      ajax: {
        url: URL_MODULO + "controlador.php",
        type: "POST",
        deferRender: false,
        data: {
          accion: "mostrar_canales",
          regional: regional,
          cedula: cedula,
          usuario: usuario,
        },
      },
      bFilter: false,
      responsive: true,
      columns: [
        { data: "id", width: "10" },
        { data: "nombre", width: "70" },
        { data: "id", width: "20" },
      ],
      columnDefs: [
        {
          targets: 0,
          data: "id",
          render: function (data, type, row) {
            if (row["permiso"] == 1) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_canal" checked/>'
              );
            } else if (row["permiso"] == 2) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_canal" />'
              );
            } else {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_canal"/>'
              );
            }
          },
        },
        {
          targets: 2,
          data: "",
          render: function (data, type, row) {
            return '<button class="btn btn-sm btn-primary v_distri"><i class="glyphicon glyphicon-plus"></i></button>';
          },
        },
      ],
      fnDrawCallback: function () {
        $("input[type=checkbox][data-permiso=2]").prop("indeterminate", true);
        $(".v_distri").unbind("click");
        $(".chk_canal").unbind("click");
        $(".chk_canal").click(function () {
          var data = dtablec.row($(this).parents("tr")).data();
          control = $(this);
          if ($(this).is(":checked")) {
            BootstrapDialog.confirm(
              "¿Esta seguro de asignar el permiso?",
              function (result) {
                if (result) {
                  Guardar_Permisos(2, regional + "_" + data["id"]);
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", false);
                  }
                }
              }
            );
          } else {
            BootstrapDialog.confirm(
              "¿Esta seguro de eliminar el permiso?",
              function (result) {
                if (result) {
                  Borrar_Permisos(2, regional + "_" + data["id"]);
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", true);
                  }
                }
              }
            );
          }
        });

        $(".v_distri").click(function () {
          var dataz = dtablec.row($(this).parents("tr")).data();
          Mostrar_Distribuidores(dataz["id"], regional);
        });
      },
    });
  } else {
    dtablec.destroy();
    Mostrar_Canales(regional);
  }
  Mostrar_tabla("canales");
}

function Mostrar_Distribuidores(canal, regional) {
  $("#info_usu, #resultado").show();
  $("#titulo").html("DISTRIBUIDORES");
  if (!$.fn.DataTable.isDataTable("#t_distribuidor")) {
    dtabled = $("#t_distribuidor").DataTable({
      ajax: {
        url: URL_MODULO + "controlador.php",
        type: "POST",
        deferRender: false,
        data: {
          accion: "mostrar_distribuidores",
          canal: canal,
          regional: regional,
          cedula: cedula,
          usuario: usuario,
        },
      },
      bFilter: false,
      responsive: true,
      columns: [
        { data: "id", width: "10" },
        { data: "nombre", width: "70" },
        { data: "id", width: "20" },
      ],
      columnDefs: [
        {
          targets: 0,
          data: "id",
          render: function (data, type, row) {
            if (row["permiso"] == 1) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_distri" checked/>'
              );
            } else if (row["permiso"] == 2) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_distri" />'
              );
            } else {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_distri"/>'
              );
            }
          },
        },
        {
          targets: 2,
          data: "",
          render: function (data, type, row) {
            return '<button class="btn btn-sm btn-primary v_territorio"><i class="glyphicon glyphicon-plus"></i></button>';
          },
        },
      ],
      fnDrawCallback: function () {
        $("input[type=checkbox][data-permiso=2]").prop("indeterminate", true);
        $(".v_territorio").unbind("click");
        $(".chk_distri").unbind("click");
        $(".chk_distri").click(function () {
          var data = dtabled.row($(this).parents("tr")).data();
          control = $(this);
          if ($(this).is(":checked")) {
            BootstrapDialog.confirm(
              "¿Esta seguro de asignar el permiso?",
              function (result) {
                if (result) {
                  Guardar_Permisos(
                    3,
                    regional + "_" + canal + "_" + data["id"]
                  );
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", false);
                  }
                }
              }
            );
          } else {
            BootstrapDialog.confirm(
              "¿Esta seguro de eliminar el permiso?",
              function (result) {
                if (result) {
                  Borrar_Permisos(3, regional + "_" + canal + "_" + data["id"]);
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", true);
                  }
                }
              }
            );
          }
        });
        $(".v_territorio").click(function () {
          var dataz = dtabled.row($(this).parents("tr")).data();
          Mostrar_Territorios(canal, regional, dataz["id"]);
        });
      },
    });
  } else {
    dtabled.destroy();
    Mostrar_Distribuidores(canal, regional);
  }

  Mostrar_tabla("distribuidor");
  $("#v_canales").attr("data-regional", regional);
}

function Mostrar_tabla(tabla) {
  $("div.table").each(function () {
    if ($(this).attr("id") == tabla) {
      $(this).show();
    } else {
      $(this).hide();
    }
  });
}

function Cargar_Arbol() {
  cedula = $("#cedulab").val();
  if (cedula != "") {
    $("#panel").hide();
    fetch(URL_MODULO + "controlador.php", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        accion: "Mostrar_Arbol",
        cc: cedula
      })
    }).then(response => response.json()).then(data => {
      $("#panel").show();
      var html = "";
      console.log(data);
      $.each(data["datos_regional"], function (index_reg, fila_reg) {
        var id_regional = fila_reg.id;
        var ico = "glyphicon glyphicon-remove-sign";
        var color = "color:red;";
        var c_estado = "";
        var botones =
          '<button type="button" data-regional="' +
          id_regional +
          '" class="btn btn-sm btn-primary-success pull-right todos_reg"><i class="glyphicon glyphicon-eye-open"></i></button>';
        if (fila_reg.permiso == 1) {
          ico = "glyphicon glyphicon-ok-sign";
          color = "color:green;";
          botones =
            '<button type="button" data-regional="' +
            id_regional +
            '" class="btn btn-sm btn-primary-dafault pull-right dtodos_reg"><i class="glyphicon glyphicon-eye-close"></i></button>';
        } else if (fila_reg.permiso == 2) {
          ico = "glyphicon glyphicon-minus-sign";
          color = "color:orange;";
          botones =
            '<button type="button" data-regional="' +
            id_regional +
            '" class="btn btn-sm btn-primary-success pull-right todos_reg"><i class="glyphicon glyphicon-eye-open"></i></button>  <button type="button" data-regional="' +
            id_regional +
            '" class="btn btn-sm btn-primary-dafault pull-right dtodos_reg"><i class="glyphicon glyphicon-eye-close"></i></button>';
        }
        if (fila_reg.estado == 0) {
          c_estado = "background:#ccc;";
        }
        html +=
          '<li class="box box-info box-bg-primary" > <a data-toggle="collapse" data-parent="#accordion" href="#col_reg_' +
          id_regional +
          '">' +
          '<div class="box-header with-border"><h3 class="panel-title"><i dat-permison-="" class="' +
          ico +
          '" style="padding-right:5px; font-size:20px; ' +
          color +
          '"></i> ' +
          fila_reg.nombre +
          "</a> " +
          botones +
          "</h3></div>";
        //Empieza Canales
        html +=
          ' <ul style="padding:0px 30px 80px 30px; ' +
          c_estado +
          '" id="col_reg_' +
          id_regional +
          '" class="nav nav-stacked collapse"><div class="box-header with-border text-center"><h4 class="panel-title title-label">Canales</h4></div>';
        $.each(data["datos_canal"], function (index_canal, fila_canal) {
          var id_canal = fila_canal.id;
          if (fila_reg.permiso == 2) {
            if (fila_canal.permiso == 1) {
              ico = "glyphicon glyphicon-ok-sign";
              color = "color:green;";
              botones =
                '<button type="button" data-regional="' +
                id_regional +
                '" data-canal = "' +
                id_canal +
                '" class="btn btn-sm btn-primary-dafault pull-right dtodos_canal"><i class="glyphicon glyphicon-eye-close"></i></button>';
            } else if (fila_canal.permiso == 2) {
              ico = "glyphicon glyphicon-minus-sign";
              color = "color:orange;";
              botones =
                '<button type="button" data-regional="' +
                id_regional +
                '" data-canal = "' +
                id_canal +
                '"  class="btn btn-sm btn-primary-success pull-right todos_canal"><i class="glyphicon glyphicon-eye-open"></i></button>  <button type="button" data-regional="' +
                id_regional +
                '" data-canal = "' +
                id_canal +
                '" class="btn btn-sm btn-primary-dafault pull-right dtodos_canal"><i class="glyphicon glyphicon-eye-close"></i></button>';
            } else if (fila_canal.permiso == 3) {
              ico = "glyphicon glyphicon-remove-sign";
              color = "color:red;";
              botones =
                '<button type="button" data-regional="' +
                id_regional +
                '" data-canal = "' +
                id_canal +
                '"  class="btn btn-sm btn-primary-success pull-right todos_canal"><i class="glyphicon glyphicon-eye-open"></i></button> ';
            }
          }
          if (fila_reg.permiso == 1) {
            botones =
              '<button type="button" data-regional="' +
              id_regional +
              '" data-canal = "' +
              id_canal +
              '" class="btn btn-sm btn-primary-dafault pull-right dtodos_canal"><i class="glyphicon glyphicon-eye-close"></i></button>';
          }
          if (fila_reg.permiso == 3) {
            botones =
              '<button type="button" data-regional="' +
              id_regional +
              '" data-canal = "' +
              id_canal +
              '"  class="btn btn-sm btn-primary-success pull-right todos_canal"><i class="glyphicon glyphicon-eye-open"></i></button> ';
          }
          if (fila_canal.estado == 0) {
            c_estado = "background:#ccc;";
          }
          $.each(data["datos_can_reg"], function (index_canreg, fila_canreg) {
            var id_reg_canreg = fila_canreg.id_regional;
            var id_can_canreg = fila_canreg.id_canal;
            if (id_reg_canreg == id_regional && id_can_canreg == id_canal) {
              html +=
                '<li class="box box-info" style="' +
                c_estado +
                '"> <a data-toggle="collapse" data-parent="#col_reg_' +
                id_regional +
                '" href="#col_reg_' +
                id_regional +
                "_col_canal_" +
                id_canal +
                '">' +
                '<div class="box-header with-border"><h3 class="panel-title"><i dat-permison-="" class="' +
                ico +
                '" style="padding-right:5px; font-size:20px; ' +
                color +
                '"></i>' +
                fila_canal.nombre +
                "</a> " +
                botones +
                " </h3></div>";
              //Empieza Distribuidor
              html +=
                '<ul style="padding:0px 10px 20px 10px; ' +
                c_estado +
                '" id="col_reg_' +
                id_regional +
                "_col_canal_" +
                id_canal +
                '" class="nav nav-stacked collapse"><div class="box-header with-border text-center"><h4 class="panel-title title-label">Distribuidores</h4></div>';
              $.each(
                data["otro"][id_regional],
                function (index_distri, fila_distri) {
                  var id_distri = fila_distri.id;
                  if (fila_canal.permiso == 2) {
                    if (fila_distri.permiso == 1) {
                      ico = "glyphicon glyphicon-ok-sign";
                      color = "color:green;";
                      botones =
                        '<button type="button" data-regional="' +
                        id_regional +
                        '" data-canal = "' +
                        id_canal +
                        '" data-distri="' +
                        id_distri +
                        '" class="btn btn-sm btn-primary-dafault pull-right dtodos_distri"><i class="glyphicon glyphicon-eye-close"></i></button>';
                    } else if (fila_distri.permiso == 2) {
                      ico = "glyphicon glyphicon-minus-sign";
                      color = "color:orange;";
                      botones =
                        '<button type="button" data-regional="' +
                        id_regional +
                        '" data-canal = "' +
                        id_canal +
                        '" data-distri="' +
                        id_distri +
                        '" class="btn btn-sm btn-primary-success pull-right todos_distri"><i class="glyphicon glyphicon-eye-open"></i></button>  <button type="button" data-regional="' +
                        id_regional +
                        '" data-canal = "' +
                        id_canal +
                        '" data-distri="' +
                        id_distri +
                        '" class="btn btn-sm btn-primary-dafault pull-right dtodos_distri"><i class="glyphicon glyphicon-eye-close"></i></button>';
                    } else if (fila_distri.permiso == 3) {
                      ico = "glyphicon glyphicon-remove-sign";
                      color = "color:red;";
                      botones =
                        '<button type="button" data-regional="' +
                        id_regional +
                        '" data-canal = "' +
                        id_canal +
                        '" data-distri="' +
                        id_distri +
                        '"  class="btn btn-sm btn-primary-success pull-right todos_distri"><i class="glyphicon glyphicon-eye-open"></i></button> ';
                    }
                  }
                  //
                  if (fila_canal.permiso == 1) {
                    botones =
                      '<button type="button" data-regional="' +
                      id_regional +
                      '" data-canal = "' +
                      id_canal +
                      '" data-distri="' +
                      id_distri +
                      '" class="btn btn-sm btn-primary-dafault pull-right dtodos_distri"><i class="glyphicon glyphicon-eye-close"></i></button>';
                  }
                  if (fila_canal.permiso == 3) {
                    botones =
                      '<button type="button" data-regional="' +
                      id_regional +
                      '" data-canal = "' +
                      id_canal +
                      '" data-distri="' +
                      id_distri +
                      '"  class="btn btn-sm btn-primary-success pull-right todos_distri"><i class="glyphicon glyphicon-eye-open"></i></button> ';
                  }
                  //
                  if (fila_reg.permiso == 1) {
                    ico = "glyphicon glyphicon-ok-sign";
                    color = "color:green;";
                    botones =
                      '<button type="button" data-regional="' +
                      id_regional +
                      '" data-canal = "' +
                      id_canal +
                      '" data-distri="' +
                      id_distri +
                      '" class="btn btn-sm btn-primary-dafault pull-right dtodos_distri"><i class="glyphicon glyphicon-eye-close"></i></button>';
                  }
                  if (fila_reg.permiso == 3) {
                    ico = "glyphicon glyphicon-remove-sign";
                    color = "color:red;";
                    botones =
                      '<button type="button" data-regional="' +
                      id_regional +
                      '" data-canal = "' +
                      id_canal +
                      '" data-distri="' +
                      id_distri +
                      '"  class="btn btn-sm btn-primary-success pull-right todos_distri"><i class="glyphicon glyphicon-eye-open"></i></button> ';
                  }
                  //
                  if (fila_distri.estado == 0) {
                    c_estado = "background:#ccc;";
                  }

                  $.each(
                    data["datos_reg_distri"],
                    function (index_r_distri, fila_r_distri) {
                      var r_id_regio = fila_r_distri.id_reg;
                      var r_id_distri = fila_r_distri.id_distri;
                      if (
                        r_id_regio == id_regional &&
                        r_id_distri == id_distri
                      ) {
                        html +=
                          '<div> <li class="box box-info"> <a style="' +
                          c_estado +
                          '" href="#" class="v_ruta" data-sion="' +
                          fila_reg.permiso +
                          "_" +
                          fila_canal.permiso +
                          "_" +
                          fila_distri.permiso +
                          '"" data-toggle="collapse" data-name="' +
                          fila_distri.nombre +
                          '" data-datos="col_reg-' +
                          id_regional +
                          "-_col_canal-" +
                          id_canal +
                          "-_col_distri-" +
                          id_distri +
                          '" data-parent="#col_reg_' +
                          id_regional +
                          "_col_canal_" +
                          id_canal +
                          '"><i dat-permison-="" class="' +
                          ico +
                          '" style="padding-right:5px; font-size:20px; ' +
                          color +
                          '"></i>' +
                          fila_distri.nombre +
                          "</a>" +
                          botones +
                          "</li></div>";
                      }
                    }
                  );
                }
              ); //Fin distribuidores
              html += "</ul>" + "</li>";
            }
          });
        }); // fin Canales

        html += "</ul>" + "</li>";
      }); // Fin Regionales

      $("#accordion").html(html);

      $(".todos_reg").click(function () {
        var id_reg = $(this).attr("data-regional");
        BootstrapDialog.confirm(
          "¿Esta seguro de asignar todos los permisos a la regional?",
          function (result) {
            if (result) {
              Guardar_Permisos(1, id_reg);
            }
          }
        );
      });
      $(".dtodos_reg").click(function () {
        var id_reg = $(this).attr("data-regional");
        BootstrapDialog.confirm(
          "¿Esta seguro de eliminar todos los permisos a la regional?",
          function (result) {
            if (result) {
              Borrar_Permisos(1, id_reg);
            }
          }
        );
      });
      //Funciones Canal
      $(".todos_canal").click(function () {
        var datos =
          $(this).attr("data-regional") + "_" + $(this).attr("data-canal");
        BootstrapDialog.confirm(
          "¿Esta seguro de asignar todos los permisos al canal?",
          function (result) {
            if (result) {
              Guardar_Permisos(2, datos);
            }
          }
        );
      });
      $(".dtodos_canal").click(function () {
        var datos =
          $(this).attr("data-regional") + "_" + $(this).attr("data-canal");
        BootstrapDialog.confirm(
          "¿Esta seguro de eliminar todos los permisos al canal?",
          function (result) {
            if (result) {
              Borrar_Permisos(2, datos);
            }
          }
        );
      });
      // Funciones Distribuidor
      $(".todos_distri").click(function () {
        var datos =
          $(this).attr("data-regional") +
          "_" +
          $(this).attr("data-canal") +
          "_" +
          $(this).attr("data-distri");
        BootstrapDialog.confirm(
          "¿Esta seguro de asignar todos los permisos al distribuidor?",
          function (result) {
            if (result) {
              Guardar_Permisos(3, datos);
            }
          }
        );
      });
      $(".dtodos_distri").click(function () {
        var datos =
          $(this).attr("data-regional") +
          "_" +
          $(this).attr("data-canal") +
          "_" +
          $(this).attr("data-distri");
        BootstrapDialog.confirm(
          "¿Esta seguro de eliminar todos los permisos al distribuidor?",
          function (result) {
            if (result) {
              Borrar_Permisos(3, datos);
            }
          }
        );
      });

      $(".v_ruta").click(function () {
        $(".modal").modal();
        Mostrar_Territorios(
          $(this).attr("data-datos"),
          $(this).attr("data-name"),
          $(this).attr("data-sion")
        );
      });

      $("a.pull-right").click(function () {
        if ($(this).attr("aria-expanded") == "false") {
          $(this)
            .find("i")
            .removeAttr("class")
            .attr("class", "glyphicon glyphicon-chevron-up");
        } else {
          $(this)
            .find("i")
            .removeAttr("class")
            .attr("class", "glyphicon glyphicon-chevron-down");
        }
      });
    }).catch(error => {
      console.error('Error:', error);
      $("#panel").show();
    });
  } else {
    Notificacion("Primero debe ingresar la cedula del usuario", "warning");
  }
}

function Mostrar_Territorios(canal, regional, distri) {
  $("#info_usu, #resultado").show();
  $("#titulo").html(alia_territorio || "RUTAS");
  if (!$.fn.DataTable.isDataTable("#t_territorios")) {
    dtablet = $("#t_territorios").DataTable({
      ajax: {
        url: URL_MODULO + "controlador.php",
        type: "POST",
        deferRender: false,
        data: {
          accion: "mostrar_territorios",
          regional: regional,
          canal: canal,
          distri: distri,
          cedula: cedula,
          usuario: usuario,
        },
      },
      bFilter: false,
      responsive: true,
      columns: [
        { data: "id", width: "10" },
        { data: "descripcion", width: "70" },
        { data: "id", width: "20" },
      ],
      columnDefs: [
        {
          targets: 0,
          data: "id",
          render: function (data, type, row) {
            if (row["permiso"] == 1) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_terr" checked/>'
              );
            } else if (row["permiso"] == 2) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_terr" />'
              );
            } else {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_terr"/>'
              );
            }
          },
        },
        {
          targets: 2,
          data: "",
          render: function (data, type, row) {
            return '<button class="btn btn-sm btn-primary v_zonas"><i class="glyphicon glyphicon-plus"></i></button>';
          },
        },
      ],
      fnDrawCallback: function () {
        $("input[type=checkbox][data-permiso=2]").prop("indeterminate", true);
        $(".v_zonas").unbind("click");
        $(".chk_terr").unbind("click");

        $(".v_zonas").click(function () {
          var dataz = dtablet.row($(this).parents("tr")).data();
          Mostrar_Zonas(regional, canal, distri, dataz["id"]);
        });

        $(".chk_terr").click(function () {
          var data = dtablet.row($(this).parents("tr")).data();
          control = $(this);
          if ($(this).is(":checked")) {
            BootstrapDialog.confirm(
              "¿Esta seguro de asignar el permiso al territorio?",
              function (result) {
                if (result) {
                  Guardar_Permisos_Territorio(
                    regional,
                    canal,
                    distri,
                    data["id"]
                  );
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", false);
                  }
                }
              }
            );
          } else {
            BootstrapDialog.confirm(
              "¿Esta seguro de eliminar el permiso al territorio?",
              function (result) {
                if (result) {
                  Borrar_Permisos_Territorio(
                    regional,
                    canal,
                    distri,
                    data["id"]
                  );
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", true);
                  }
                }
              }
            );
          }
        });
      },
    });
  } else {
    dtablet.destroy();
    Mostrar_Territorios(canal, regional, distri);
  }
  Mostrar_tabla("territorios");
  $("#v_distri").attr("data-regional", regional);
  $("#v_distri").attr("data-canal", canal);
}

function Mostrar_Zonas(regional, canal, distri, territorio) {
  $("#info_usu, #resultado").show();
  $("#titulo").html(alia_zona || "CIRCUITOS");
  if (!$.fn.DataTable.isDataTable("#t_zonas")) {
    dtablez = $("#t_zonas").DataTable({
      ajax: {
        url: URL_MODULO + "controlador.php",
        type: "POST",
        deferRender: false,
        data: {
          accion: "mostrar_zonas",
          regional: regional,
          canal: canal,
          distri: distri,
          territorio: territorio,
          cedula: cedula,
          usuario: usuario,
        },
      },
      bFilter: false,
      responsive: true,
      columns: [
        { data: "id", width: "10" },
        { data: "nombre", width: "70" },
        { data: "id", width: "20" },
      ],
      columnDefs: [
        {
          targets: 0,
          data: "id",
          render: function (data, type, row) {
            if (row["permiso"] == 1) {
              return (
                '<input type="checkbox" class="chk_zon" data-permiso="' +
                row["permiso"] +
                '" checked/>'
              );
            } else if (row["permiso"] == 2) {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_zon" />'
              );
            } else {
              return (
                '<input type="checkbox" data-permiso="' +
                row["permiso"] +
                '" class="chk_zon"/>'
              );
            }
          },
        },
        {
          targets: 2,
          data: "",
          render: function (data, type, row) {
            return '<button class="btn btn-sm btn-primary v_punto"><i class="glyphicon glyphicon-plus"></i></button>';
          },
        },
      ],
      fnDrawCallback: function () {
        $("input[type=checkbox][data-permiso=2]").prop("indeterminate", true);
        $(".v_punto").unbind("click");
        $(".chk_zon").unbind("click");
        $(".v_punto").click(function () {
          var data = dtablez.row($(this).parents("tr")).data();
          Mostrar_Puntos(regional, canal, distri, territorio, data["id"]);
        });

        $(".chk_zon").click(function () {
          var data = dtablez.row($(this).parents("tr")).data();
          control = $(this);
          if ($(this).is(":checked")) {
            BootstrapDialog.confirm(
              "¿Esta seguro de asignar el permiso a la zona?",
              function (result) {
                if (result) {
                  Guardar_Permisos_Zonas(
                    regional,
                    canal,
                    distri,
                    territorio,
                    data["id"]
                  );
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", false);
                  }
                }
              }
            );
          } else {
            BootstrapDialog.confirm(
              "¿Esta seguro de eliminar el permiso a la zona?",
              function (result) {
                if (result) {
                  Borrar_Permisos_Zonas(
                    regional,
                    canal,
                    distri,
                    territorio,
                    data["id"]
                  );
                } else {
                  if ($(control).attr("data-permiso") == 2) {
                    $(control).prop("indeterminate", true);
                  } else {
                    $(control).prop("checked", true);
                  }
                }
              }
            );
          }
        });
      },
    });
  } else {
    dtablez.destroy();
    Mostrar_Zonas(regional, canal, distri, territorio);
  }
  Mostrar_tabla("zonas");
  $("#v_circuito").attr("data-regional", regional);
  $("#v_circuito").attr("data-canal", canal);
  $("#v_circuito").attr("data-distri", distri);
}

function Mostrar_Puntos(regional, canal, distri, territorio, zona) {
  $("#info_usu, #resultado").show();
  $("#titulo").html("PUNTOS");
  if (!$.fn.DataTable.isDataTable("#t_puntos")) {
    dtablep = $("#t_puntos").DataTable({
      ajax: {
        url: URL_MODULO + "controlador.php",
        type: "POST",
        deferRender: false,
        data: {
          accion: "mostrar_puntos",
          regional: regional,
          canal: canal,
          distri: distri,
          territorio: territorio,
          zona: zona,
          cedula: cedula,
          usuario: usuario,
        },
      },
      bFilter: false,
      responsive: true,
      columns: [
        //{ "data": "idpos"},
        { data: "idpos" },
        { data: "razon" },
        { data: "cedula" },
        { data: "nombre_cli" },
      ],
      /*"columnDefs": [
        {
           "targets": 0,
           "data": "idpos",
          render: function ( data, type, row ) {
             if(row['permiso'] == 1)
             {
                 return '<input type="checkbox" data-permiso="'+row['permiso']+'" class="chk_pun" checked/>';
             }
             else if(row['permiso'] == 2)
             {
                 return '<input type="checkbox" data-permiso="'+row['permiso']+'" class="chk_pun" />';
             }
             else
             {
                 return '<input type="checkbox" data-permiso="'+row['permiso']+'" class="chk_pun"/>';
             }
          }
         }],*/
      /*fnDrawCallback: function () {
        $("input[type=checkbox][data-permiso=2]").prop('indeterminate',true);
        $(".chk_pun").unbind("click");
        $(".chk_pun").click(function() {
          var data = dtablep.row($(this).parents('tr')).data();
          control = $(this);
          if($(this).is(":checked"))
          {
            BootstrapDialog.confirm("¿Esta seguro de asignar el permiso a el punto?", function(result){
            if(result) {
               Guardar_Permisos_Puntos(regional,canal,distri,territorio,zona,data['idpos']);
              }
              else{
                $(control).prop("checked",false);
              }
            });
          }
          else
          {
            BootstrapDialog.confirm("¿Esta seguro de eliminar el permiso a el punto?", function(result){
            if(result) {
               Borrar_Permisos_Puntos(regional,canal,distri,territorio,zona,data['idpos']);
              }
              else{
                $(control).prop("checked",true);
              }
            });
          }
        });
          }*/
    });
  } else {
    dtablep.destroy();
    Mostrar_Puntos(regional, canal, distri, territorio, zona);
  }
  Mostrar_tabla("puntos");
  $("#v_ruta").attr("data-regional", regional);
  $("#v_ruta").attr("data-canal", canal);
  $("#v_ruta").attr("data-distri", distri);
  $("#v_ruta").attr("data-territorio", territorio);
}

function Guardar_Permisos(tipo, id) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Guardar_Permisos",
      tipo: tipo,
      id: id,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

function Borrar_Permisos(tipo, id) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Borrar_Permisos",
      tipo: tipo,
      id: id,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}
//Territorios
function Borrar_Permisos_Territorio(regional, canal, distri, id_terr) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Borrar_Permisos_Territorio",
      regional: regional,
      canal: canal,
      distri: distri,
      id_terr: id_terr,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

function Guardar_Permisos_Territorio(regional, canal, distri, id_terr) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Guardar_Permisos_Territorio",
      regional: regional,
      canal: canal,
      distri: distri,
      id_terr: id_terr,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}
/*-------------------------*/

// Zonas
function Borrar_Permisos_Zonas(regional, canal, distri, territorio, zona) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Borrar_Permisos_Zonas",
      regional: regional,
      canal: canal,
      distri: distri,
      territorio: territorio,
      zona: zona,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}
function Guardar_Permisos_Zonas(regional, canal, distri, territorio, zona) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Guardar_Permisos_Zonas",
      regional: regional,
      canal: canal,
      distri: distri,
      territorio: territorio,
      zona: zona,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}
/*-------------------------*/

//Puntos
function Borrar_Permisos_Puntos(
  regional,
  canal,
  distri,
  territorio,
  zona,
  punto
) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Borrar_Permisos_Puntos",
      regional: regional,
      canal: canal,
      distri: distri,
      territorio: territorio,
      zona: zona,
      punto: punto,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}
function Guardar_Permisos_Puntos(
  regional,
  canal,
  distri,
  territorio,
  zona,
  punto
) {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Guardar_Permisos_Puntos",
      regional: regional,
      canal: canal,
      distri: distri,
      territorio: territorio,
      zona: zona,
      punto: punto,
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    if (data["res"] == "1") {
      Notificacion(data["msg"], "success");
    } else {
      Notificacion(data["msg"], "error");
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}
/*-------------------------*/

function Mostrar_Usuario() {
  fetch(URL_MODULO + "controlador.php", {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accion: "Mostrar_Usuario",
      cedula: cedula,
      usuario: usuario
    })
  }).then(response => response.json()).then(data => {
    $("#n_usuario").html("&nbsp;&nbsp;" + data[0]["nombre_usuario"]);
    $("#c_usuario").html("&nbsp;&nbsp;" + data[0]["cedula"]);
    $("#info_usu, #resultado").show();
  }).catch(error => {
    console.error('Error:', error);
  });
}
